// ask2.2.2 Javascript New Class Service Generation Template
// The task description covers only the initial requirements. For training purposes, the task is similar to real-world situations, with the developer learning about specific requirements during the development process. Use the test results to examine and identify all requirements.
// In this task, you're challenged with seeking assistance from Copi<PERSON> to construct a TypeScript class/service that combines methods and integrates the Factory Method design pattern:

// Employee Factory Class: create a class named EmployeeFactory. This class will have a method called createEmployee.
// Different Employee Types: the createEmployee method should accept a type parameter (like "fulltime", "parttime", "temporary", "contractor"). Depending on the provided type, it should return an instance of the respective class. Each employee type class (e.g., FullTimeEmployee, PartTimeEmployee, etc.) should have a property hourly to store hourly rates.
// Display Method: every employee instance should have a method say to log their type and hourly rate.

// Define the Employee interface
export interface Employee {
    hourly: number;
    say(): void;
}

// Define the different Employee types
export class FullTimeEmployee implements Employee {
    hourly = 40;

    say(): void {
        console.log(
            `I am a full-time employee with an hourly rate of $${this.hourly}`
        );
    }
}

export class PartTimeEmployee implements Employee {
    hourly = 20;

    say(): void {
        console.log(
            `I am a part-time employee with an hourly rate of $${this.hourly}`
        );
    }
}

export class TemporaryEmployee implements Employee {
    hourly = 10;

    say(): void {
        console.log(
            `I am a temporary employee with an hourly rate of $${this.hourly}`
        );
    }
}

export class ContractorEmployee implements Employee {
    hourly = 30;

    say(): void {
        console.log(
            `I am a contractor employee with an hourly rate of $${this.hourly}`
        );
    }
}

// Define the EmployeeFactory class
export class EmployeeFactory {
    createEmployee(type: string): Employee | null {
        switch (type) {
            case "fulltime":
                return new FullTimeEmployee();
            case "parttime":
                return new PartTimeEmployee();
            case "temporary":
                return new TemporaryEmployee();
            case "contractor":
                return new ContractorEmployee();
            default:
                return null;
        }
    }
}
