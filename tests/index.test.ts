import { EmployeeFactory, FullTimeEmployee, PartTimeEmployee, TemporaryEmployee, ContractorEmployee } from '../src';

describe('EmployeeFactory', () => {
    let factory: EmployeeFactory;

    beforeEach(() => {
        factory = new EmployeeFactory();
    });

    it('should create a full-time employee', () => {
        const employee = factory.createEmployee('fulltime');
        expect(employee).toBeInstanceOf(FullTimeEmployee);

    });

    it('should create a part-time employee', () => {
        const employee = factory.createEmployee('parttime');
        expect(employee).toBeInstanceOf(PartTimeEmployee);

    });

    it('should create a temporary employee', () => {
        const employee = factory.createEmployee('temporary');
        expect(employee).toBeInstanceOf(TemporaryEmployee);

    });

    it('should create a contractor employee', () => {
        const employee = factory.createEmployee('contractor');
        expect(employee).toBeInstanceOf(ContractorEmployee);

    });

    it('should return null for an unknown type', () => {
        const employee = factory.createEmployee('unknown');
        expect(employee).toBeNull();
    });
});
