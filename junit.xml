<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="5" failures="0" errors="0" time="1.529">
  <testsuite name="EmployeeFactory" errors="0" failures="0" skipped="0" timestamp="2025-06-17T12:42:38" time="1.158" tests="5">
    <testcase classname="EmployeeFactory should create a full-time employee" name="EmployeeFactory should create a full-time employee" time="0.002">
    </testcase>
    <testcase classname="EmployeeFactory should create a part-time employee" name="EmployeeFactory should create a part-time employee" time="0">
    </testcase>
    <testcase classname="EmployeeFactory should create a temporary employee" name="EmployeeFactory should create a temporary employee" time="0.001">
    </testcase>
    <testcase classname="EmployeeFactory should create a contractor employee" name="EmployeeFactory should create a contractor employee" time="0">
    </testcase>
    <testcase classname="EmployeeFactory should return null for an unknown type" name="EmployeeFactory should return null for an unknown type" time="0.001">
    </testcase>
  </testsuite>
</testsuites>