# Task2.2.2 Javascript New Class Service Generation Template

*The task description covers only the initial requirements. For training purposes, the task is similar to real-world situations, with the developer learning about specific requirements during the development process. Use the test results to examine and identify all requirements.*

In this task, you're challenged with seeking assistance from Copilot to construct a TypeScript class/service that combines methods and integrates the Factory Method design pattern:

* Employee Factory Class: create a class named **EmployeeFactory**. This class will have a method called **createEmployee**.
* Different Employee Types: the **createEmployee** method should accept a type parameter (like "fulltime", "parttime", "temporary", "contractor"). Depending on the provided type, it should return an instance of the respective class. Each employee type class (e.g., FullTimeEmployee, PartTimeEmployee, etc.) should have a property hourly to store hourly rates.
* Display Method: every employee instance should have a method say to log their type and hourly rate.