{"name": "task2.2.2-javascript-ts-class-service-generation-template", "version": "1.0.0", "description": "", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"start": "node dist/index.js", "build": "tsc", "test": "jest --ci --reporters=default --reporters=jest-junit"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/jest": "^29.5.5", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2", "jest-junit": "^16.0.0"}}